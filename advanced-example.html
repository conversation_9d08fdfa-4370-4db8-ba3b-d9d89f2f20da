<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlayerJS Advanced Features Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 10px;
        }
        
        .demo-title {
            font-size: 20px;
            font-weight: bold;
            color: #555;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }
        
        .demo-title::before {
            content: "🎯";
            margin-right: 10px;
        }
        
        .player-wrapper {
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .controls-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: transform 0.2s;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        
        .info-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        
        .playlist-demo {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .status-display {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            min-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PlayerJS Advanced Demo</h1>
        
        <!-- Playlist Demo -->
        <div class="demo-section">
            <div class="demo-title">Playlist với Multiple Videos</div>
            <div class="info-box">
                <strong>Tính năng:</strong> Player có thể phát danh sách video liên tiếp với điều hướng tự động.
            </div>
            <div class="player-wrapper">
                <div id="playlistPlayer"></div>
            </div>
            <div class="controls-panel">
                <button onclick="playlistNext()">⏭️ Next Video</button>
                <button onclick="playlistPrev()">⏮️ Previous Video</button>
                <button onclick="playlistShuffle()">🔀 Shuffle</button>
                <button onclick="playlistLoop()">🔁 Loop Toggle</button>
            </div>
        </div>

        <!-- HLS Stream Demo -->
        <div class="demo-section">
            <div class="demo-title">HLS Live Stream</div>
            <div class="info-box">
                <strong>Tính năng:</strong> Hỗ trợ phát stream trực tiếp HLS với chất lượng adaptive.
            </div>
            <div class="player-wrapper">
                <div id="hlsPlayer"></div>
            </div>
            <div class="controls-panel">
                <button onclick="changeQuality('auto')">📺 Auto Quality</button>
                <button onclick="changeQuality('720p')">🎬 720p</button>
                <button onclick="changeQuality('480p')">📱 480p</button>
                <button onclick="toggleSubtitles()">📝 Subtitles</button>
            </div>
        </div>

        <!-- Custom Skin Demo -->
        <div class="demo-section">
            <div class="demo-title">Custom Skin & Effects</div>
            <div class="info-box">
                <strong>Tính năng:</strong> Tùy chỉnh giao diện với skin và hiệu ứng đặc biệt.
            </div>
            <div class="player-wrapper">
                <div id="customPlayer"></div>
            </div>
            <div class="controls-panel">
                <button onclick="changeSkin('modern')">🎨 Modern Skin</button>
                <button onclick="changeSkin('retro')">📼 Retro Skin</button>
                <button onclick="toggleEffect('snow')">❄️ Snow Effect</button>
                <button onclick="toggleEffect('blur')">🌫️ Blur Effect</button>
            </div>
        </div>

        <!-- API Demo -->
        <div class="demo-section">
            <div class="demo-title">API Control & Events</div>
            <div class="info-box">
                <strong>Tính năng:</strong> Điều khiển player qua JavaScript API và lắng nghe events.
            </div>
            <div class="controls-panel">
                <button onclick="seekTo(30)">⏩ Seek to 30s</button>
                <button onclick="setSpeed(1.5)">⚡ Speed 1.5x</button>
                <button onclick="setSpeed(0.5)">🐌 Speed 0.5x</button>
                <button onclick="takeScreenshot()">📸 Screenshot</button>
                <button onclick="toggleFullscreen()">🖥️ Fullscreen</button>
                <button onclick="enablePiP()">📺 Picture-in-Picture</button>
            </div>
            <div class="status-display" id="statusLog">
                <div>🟢 PlayerJS Advanced Demo Ready</div>
                <div>📊 Waiting for player events...</div>
            </div>
        </div>
    </div>

    <!-- PlayerJS Library -->
    <script src="https://cdn.playerjs.com/player.js"></script>
    
    <script>
        // Global player instances
        let playlistPlayer, hlsPlayer, customPlayer;
        let currentSkin = 'modern';
        let effectsEnabled = {};

        // Initialize all players
        document.addEventListener('DOMContentLoaded', function() {
            initializePlayers();
            setupEventListeners();
        });

        function initializePlayers() {
            // Playlist Player
            playlistPlayer = new Playerjs({
                id: "playlistPlayer",
                playlist: [
                    {
                        title: "Big Buck Bunny",
                        file: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                        poster: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg"
                    },
                    {
                        title: "Elephant Dream",
                        file: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
                        poster: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ElephantsDream.jpg"
                    },
                    {
                        title: "For Bigger Blazes",
                        file: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
                        poster: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/ForBiggerBlazes.jpg"
                    }
                ],
                autoplay: false,
                controls: true,
                fullscreen: true,
                skin: "playlist"
            });

            // HLS Player (using a demo HLS stream)
            hlsPlayer = new Playerjs({
                id: "hlsPlayer",
                file: "https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8",
                autoplay: false,
                controls: true,
                fullscreen: true,
                skin: "hls"
            });

            // Custom Player with effects
            customPlayer = new Playerjs({
                id: "customPlayer",
                file: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                poster: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg",
                autoplay: false,
                controls: true,
                fullscreen: true,
                skin: "modern"
            });

            logStatus("✅ All players initialized successfully");
        }

        function setupEventListeners() {
            // Listen to all PlayerJS events
            document.addEventListener('playerjs', function(e) {
                const detail = e.detail;
                logStatus(`🎬 Event: ${detail.event} | Player: ${detail.id} | Time: ${detail.time || 'N/A'}s`);
                
                // Handle specific events
                switch(detail.event) {
                    case 'ready':
                        logStatus(`🟢 Player ${detail.id} is ready`);
                        break;
                    case 'play':
                        logStatus(`▶️ Playing: ${detail.file || 'Unknown'}`);
                        break;
                    case 'pause':
                        logStatus(`⏸️ Paused at ${detail.time}s`);
                        break;
                    case 'ended':
                        logStatus(`🏁 Video ended`);
                        break;
                    case 'error':
                        logStatus(`❌ Error: ${detail.error}`);
                        break;
                }
            });
        }

        // Playlist Controls
        function playlistNext() {
            if (playlistPlayer) {
                playlistPlayer.api("next");
                logStatus("⏭️ Switching to next video");
            }
        }

        function playlistPrev() {
            if (playlistPlayer) {
                playlistPlayer.api("prev");
                logStatus("⏮️ Switching to previous video");
            }
        }

        function playlistShuffle() {
            if (playlistPlayer) {
                playlistPlayer.api("shuffle");
                logStatus("🔀 Playlist shuffled");
            }
        }

        function playlistLoop() {
            if (playlistPlayer) {
                playlistPlayer.api("loop");
                logStatus("🔁 Loop mode toggled");
            }
        }

        // Quality Controls
        function changeQuality(quality) {
            if (hlsPlayer) {
                hlsPlayer.api("quality", quality);
                logStatus(`📺 Quality changed to: ${quality}`);
            }
        }

        function toggleSubtitles() {
            if (hlsPlayer) {
                hlsPlayer.api("subtitle");
                logStatus("📝 Subtitles toggled");
            }
        }

        // Skin & Effects
        function changeSkin(skin) {
            if (customPlayer) {
                customPlayer.api("skin", skin);
                currentSkin = skin;
                logStatus(`🎨 Skin changed to: ${skin}`);
            }
        }

        function toggleEffect(effect) {
            if (customPlayer) {
                effectsEnabled[effect] = !effectsEnabled[effect];
                customPlayer.api("effect", effect, effectsEnabled[effect]);
                logStatus(`${effectsEnabled[effect] ? '✅' : '❌'} Effect ${effect}: ${effectsEnabled[effect] ? 'ON' : 'OFF'}`);
            }
        }

        // Advanced API Controls
        function seekTo(seconds) {
            if (customPlayer) {
                customPlayer.api("seek", seconds);
                logStatus(`⏩ Seeked to ${seconds} seconds`);
            }
        }

        function setSpeed(speed) {
            if (customPlayer) {
                customPlayer.api("speed", speed);
                logStatus(`⚡ Playback speed set to ${speed}x`);
            }
        }

        function takeScreenshot() {
            if (customPlayer) {
                customPlayer.api("screenshot");
                logStatus("📸 Screenshot taken");
            }
        }

        function toggleFullscreen() {
            if (customPlayer) {
                customPlayer.api("fullscreen");
                logStatus("🖥️ Fullscreen toggled");
            }
        }

        function enablePiP() {
            if (customPlayer) {
                customPlayer.api("pip");
                logStatus("📺 Picture-in-Picture mode enabled");
            }
        }

        // Utility function to log status
        function logStatus(message) {
            const statusLog = document.getElementById('statusLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            statusLog.appendChild(logEntry);
            statusLog.scrollTop = statusLog.scrollHeight;
            
            // Keep only last 20 entries
            while (statusLog.children.length > 20) {
                statusLog.removeChild(statusLog.firstChild);
            }
        }
    </script>
</body>
</html>
