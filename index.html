<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlayerJS Example - Video Player Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .player-section {
            margin-bottom: 40px;
        }
        
        .player-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #555;
        }
        
        .player-container {
            background: #000;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        
        .description {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            margin-bottom: 20px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        
        .feature-card h3 {
            color: #007bff;
            margin-top: 0;
        }
        
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #0056b3;
        }
        
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            overflow-x: auto;
        }
        
        .code-example pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 PlayerJS Demo - HTML5 Video Player</h1>
        
        <div class="description">
            <strong>Về PlayerJS:</strong> PlayerJS là một công cụ tạo HTML5 video/audio player miễn phí với hơn 500 cài đặt tùy chỉnh. 
            Hỗ trợ MP4, HLS, DASH, YouTube, Chromecast, Airplay và nhiều tính năng khác.
        </div>

        <!-- Video Player 1 - Basic -->
        <div class="player-section">
            <div class="player-title">📹 Video Player Cơ Bản</div>
            <div class="player-container">
                <div id="player1"></div>
            </div>
            <div class="controls">
                <button onclick="playVideo()">▶️ Play</button>
                <button onclick="pauseVideo()">⏸️ Pause</button>
                <button onclick="setVolume(0.5)">🔉 Volume 50%</button>
                <button onclick="setVolume(1)">🔊 Volume 100%</button>
            </div>
        </div>

        <!-- Audio Player -->
        <div class="player-section">
            <div class="player-title">🎵 Audio Player</div>
            <div class="player-container">
                <div id="player2"></div>
            </div>
        </div>

        <!-- YouTube Player -->
        <div class="player-section">
            <div class="player-title">📺 YouTube Player</div>
            <div class="player-container">
                <div id="player3"></div>
            </div>
        </div>

        <!-- Code Examples -->
        <div class="player-section">
            <div class="player-title">💻 Code Examples</div>
            
            <h4>1. Cách tạo player cơ bản:</h4>
            <div class="code-example">
                <pre>
// Tạo player đơn giản
var player = new Playerjs({
    id: "player1",
    file: "video.mp4"
});
                </pre>
            </div>

            <h4>2. Player với nhiều tùy chọn:</h4>
            <div class="code-example">
                <pre>
// Player với cấu hình nâng cao
var player = new Playerjs({
    id: "player1",
    file: "video.mp4",
    poster: "thumbnail.jpg",
    autoplay: false,
    volume: 0.8,
    skin: "modern",
    controls: true,
    fullscreen: true
});
                </pre>
            </div>

            <h4>3. Sử dụng API để điều khiển:</h4>
            <div class="code-example">
                <pre>
// Điều khiển player qua API
player.api("play");           // Phát video
player.api("pause");          // Tạm dừng
player.api("volume", 50);     // Đặt âm lượng 50%
player.api("seek", 30);       // Chuyển đến giây thứ 30
                </pre>
            </div>
        </div>

        <!-- Features -->
        <div class="features">
            <div class="feature-card">
                <h3>🎨 Tùy Chỉnh Giao Diện</h3>
                <p>Hơn 500 cài đặt để tùy chỉnh giao diện player theo ý muốn. Nhiều skin có sẵn hoặc tạo skin riêng.</p>
            </div>
            
            <div class="feature-card">
                <h3>📱 Responsive</h3>
                <p>Player tự động thích ứng với mọi kích thước màn hình, từ desktop đến mobile.</p>
            </div>
            
            <div class="feature-card">
                <h3>🌐 Đa Định Dạng</h3>
                <p>Hỗ trợ MP4, MP3, HLS, DASH, YouTube, Vimeo và nhiều định dạng khác.</p>
            </div>
            
            <div class="feature-card">
                <h3>📊 Analytics</h3>
                <p>Tích hợp Google Analytics để theo dõi thống kê xem video.</p>
            </div>
            
            <div class="feature-card">
                <h3>📺 Casting</h3>
                <p>Hỗ trợ Chromecast, Airplay và Picture-in-Picture mode.</p>
            </div>
            
            <div class="feature-card">
                <h3>🚀 Hiệu Suất Cao</h3>
                <p>Chỉ một file .js duy nhất, tải nhanh và hoạt động offline.</p>
            </div>
        </div>
    </div>

    <!-- PlayerJS Library -->
    <script src="https://cdn.playerjs.com/player.js"></script>
    
    <script>
        // Global player variable for API control
        let mainPlayer;

        // Initialize players when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Video Player 1 - Basic MP4
            mainPlayer = new Playerjs({
                id: "player1",
                file: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
                poster: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg",
                autoplay: false,
                volume: 0.8,
                controls: true,
                fullscreen: true,
                skin: "modern"
            });

            // Audio Player
            new Playerjs({
                id: "player2",
                file: "https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3",
                autoplay: false,
                volume: 0.6,
                controls: true,
                skin: "audio"
            });

            // YouTube Player
            new Playerjs({
                id: "player3",
                file: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                autoplay: false,
                controls: true,
                fullscreen: true
            });
        });

        // API Control Functions
        function playVideo() {
            if (mainPlayer) {
                mainPlayer.api("play");
            }
        }

        function pauseVideo() {
            if (mainPlayer) {
                mainPlayer.api("pause");
            }
        }

        function setVolume(volume) {
            if (mainPlayer) {
                mainPlayer.api("volume", Math.round(volume * 100));
            }
        }

        // Event Listeners
        document.addEventListener('playerjs', function(e) {
            console.log('PlayerJS Event:', e.detail);
            
            // Handle different events
            switch(e.detail.event) {
                case 'ready':
                    console.log('Player is ready');
                    break;
                case 'play':
                    console.log('Video started playing');
                    break;
                case 'pause':
                    console.log('Video paused');
                    break;
                case 'ended':
                    console.log('Video ended');
                    break;
            }
        });
    </script>
</body>
</html>
