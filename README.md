# PlayerJS Demo Examples

<PERSON><PERSON><PERSON> là bộ ví dụ demo về cách sử dụng PlayerJS - một công cụ tạo HTML5 video/audio player mạnh mẽ và miễn phí.

## 📁 Files trong project

- `index.html` - Demo cơ bản với video, audio và YouTube player
- `advanced-example.html` - Demo các tính năng nâng cao như playlist, HLS, custom skin
- `README.md` - Hướng dẫn sử dụng (file này)

## 🚀 Cách chạy demo

1. **Chạy local server:**
   ```bash
   # Sử dụng Python
   python -m http.server 8000
   
   # Hoặc sử dụng Node.js
   npx http-server
   
   # Hoặc sử dụng PHP
   php -S localhost:8000
   ```

2. **Mở trình duyệt:**
   - Demo cơ bản: `http://localhost:8000/index.html`
   - Demo nâng cao: `http://localhost:8000/advanced-example.html`

## 🎯 Tính năng được demo

### Demo Cơ Bản (index.html)
- ✅ Video player với MP4
- ✅ Audio player với MP3
- ✅ YouTube video player
- ✅ API controls (play, pause, volume)
- ✅ Event listeners
- ✅ Responsive design

### Demo Nâng Cao (advanced-example.html)
- ✅ Playlist với multiple videos
- ✅ HLS live streaming
- ✅ Custom skins và effects
- ✅ Quality selection
- ✅ Speed control
- ✅ Screenshot capture
- ✅ Picture-in-Picture mode
- ✅ Fullscreen toggle
- ✅ Real-time event logging

## 🛠️ Cách tích hợp PlayerJS vào project

### 1. Thêm thư viện
```html
<script src="https://cdn.playerjs.com/player.js"></script>
```

### 2. Tạo container cho player
```html
<div id="myPlayer"></div>
```

### 3. Khởi tạo player
```javascript
var player = new Playerjs({
    id: "myPlayer",
    file: "path/to/your/video.mp4"
});
```

## 📋 Các tùy chọn cấu hình phổ biến

```javascript
var player = new Playerjs({
    id: "player",                    // ID của container
    file: "video.mp4",              // Đường dẫn video
    poster: "thumbnail.jpg",         // Ảnh thumbnail
    autoplay: false,                // Tự động phát
    volume: 0.8,                    // Âm lượng (0-1)
    controls: true,                 // Hiển thị controls
    fullscreen: true,               // Cho phép fullscreen
    skin: "modern",                 // Giao diện
    width: "100%",                  // Chiều rộng
    height: "400px"                 // Chiều cao
});
```

## 🎮 API Controls

```javascript
// Điều khiển phát
player.api("play");              // Phát video
player.api("pause");             // Tạm dừng
player.api("stop");              // Dừng

// Điều khiển âm thanh
player.api("volume", 50);        // Đặt âm lượng 50%
player.api("mute");              // Tắt tiếng

// Điều khiển thời gian
player.api("seek", 30);          // Chuyển đến giây 30
player.api("speed", 1.5);        // Tốc độ 1.5x

// Chế độ hiển thị
player.api("fullscreen");        // Fullscreen
player.api("pip");               // Picture-in-Picture
```

## 📊 Event Listeners

```javascript
document.addEventListener('playerjs', function(e) {
    console.log('Event:', e.detail.event);
    console.log('Time:', e.detail.time);
    console.log('Duration:', e.detail.duration);
});
```

## 🎨 Các loại file được hỗ trợ

### Video
- MP4, WebM, OGV
- HLS (.m3u8)
- DASH (.mpd)
- YouTube URLs
- Vimeo URLs

### Audio
- MP3, OGG, WAV
- AAC, FLAC
- HLS audio streams

## 🌟 Tính năng nổi bật của PlayerJS

1. **Miễn phí hoàn toàn** - Không watermark, không quảng cáo
2. **Hơn 500 cài đặt** - Tùy chỉnh mọi thứ theo ý muốn
3. **Chỉ 1 file .js** - Dễ dàng tích hợp
4. **Hoạt động offline** - Không phụ thuộc server
5. **Responsive** - Tự động thích ứng mọi thiết bị
6. **API mạnh mẽ** - Điều khiển mọi thứ qua JavaScript
7. **Hỗ trợ casting** - Chromecast, Airplay
8. **Analytics** - Tích hợp Google Analytics

## 🔧 Tùy chỉnh nâng cao

### Tạo playlist
```javascript
var player = new Playerjs({
    id: "player",
    playlist: [
        {
            title: "Video 1",
            file: "video1.mp4",
            poster: "thumb1.jpg"
        },
        {
            title: "Video 2", 
            file: "video2.mp4",
            poster: "thumb2.jpg"
        }
    ]
});
```

### Custom skin
```javascript
var player = new Playerjs({
    id: "player",
    file: "video.mp4",
    skin: "custom",
    colors: {
        primary: "#ff6b6b",
        secondary: "#4ecdc4"
    }
});
```

## 📚 Tài liệu tham khảo

- [PlayerJS Official Website](https://playerjs.com/)
- [PlayerJS Documentation](https://playerjs.com/docs)
- [PlayerJS Builder](https://playerjs.com/#signup) - Tạo player tùy chỉnh online

## 💡 Tips sử dụng

1. **Tối ưu performance**: Chỉ load những module cần thiết
2. **SEO friendly**: Sử dụng poster và metadata
3. **Mobile first**: Test trên mobile trước
4. **Accessibility**: Thêm subtitles và keyboard controls
5. **Analytics**: Theo dõi engagement để tối ưu content

## 🤝 Hỗ trợ

Nếu bạn gặp vấn đề hoặc có câu hỏi:
- Email: <EMAIL>
- Documentation: https://playerjs.com/docs
- Community: Tham gia các forum về web development

---

**Lưu ý**: Các video demo sử dụng trong examples này là từ Google's sample videos và các nguồn public, phù hợp cho việc testing và demo.
